package dao

import (
	"context"

	"go-common/library/conf/paladin.v2"
	"go-common/library/database/sql"
	"go-common/library/log"

	"recommend-base/internal/model"
	"recommend-base/internal/model/liverecommendedelements"
	"recommend-base/internal/model/mrecommendedelements"
	"recommend-base/internal/model/mrecommendedelementsdailyexposure"
	"recommend-base/internal/model/mrecommendedexposurelevel"
)

const (
	_sqlQueryRoomByStatus      = "SELECT room_id, catalog_id, title, intro, live_start_time, create_time, user_id, score FROM live WHERE status = ? AND cover IS NOT NULL AND cover <> '' ORDER BY id DESC LIMIT ? OFFSET ?"
	_sqlQueryRoomCountByStatus = "SELECT COUNT(*) FROM live WHERE status = ? AND cover IS NOT NULL AND cover <> ''"
)

func NewDB() (db *sql.DB, cf func(), err error) {
	var (
		cfg sql.Config
		ct  paladin.TOML
	)
	if err = paladin.Get("db.toml").Unmarshal(&ct); err != nil {
		return
	}
	if err = ct.Get("Client").UnmarshalTOML(&cfg); err != nil {
		return
	}
	db = sql.NewMySQL(&cfg)
	cf = func() { db.Close() }
	return
}

// RoomBaseInfo room base info
type RoomBaseInfo struct {
	RoomID        int64
	UserID        int64
	CatalogID     int64
	RoomTitle     string
	CreateTime    int64
	LiveStartTime int64
	Score         int64
}

// RoomList room list
type RoomList struct {
	List       []*RoomBaseInfo
	Pagination *Pagination
}

// Pagination pagination
type Pagination struct {
	MaxPage  int64
	Count    int64
	P        int64
	PageSize int64
}

// RawLivingRoomsInfo get data from db
func (d *dao) RawLivingRoomsInfo(ctx context.Context, page int64, pageSize int64) (res *RoomList, err error) {
	dbResult, allCount, err := d.queryLivingRoomInfo(ctx, page, pageSize)
	if err != nil {
		return
	}

	res = new(RoomList)
	res.List = make([]*RoomBaseInfo, 0)
	for _, v := range dbResult {
		res.List = append(res.List, &RoomBaseInfo{
			RoomID:        v.RoomID,
			UserID:        v.UserID,
			CatalogID:     v.CatalogID,
			RoomTitle:     v.Title,
			CreateTime:    v.CreateTime,
			LiveStartTime: v.LiveStartTime,
			Score:         v.Score,
		})
	}

	maxPage := allCount / pageSize
	if allCount%pageSize != 0 {
		maxPage += 1
	}

	res.Pagination = &Pagination{
		MaxPage:  maxPage,
		Count:    allCount,
		P:        page,
		PageSize: pageSize,
	}

	return
}

// get data from db
func (d *dao) queryLivingRoomInfo(ctx context.Context, page int64, pageSize int64) (res []*model.Live, allCount int64, err error) {
	if err = d.db.QueryRow(ctx, _sqlQueryRoomCountByStatus, model.LiveStatusOpen).Scan(&allCount); err != nil {
		log.Error("RawGetAllLivingRoomsInfo.QueryRow error(%+v)", err)
		return
	}

	var rows *sql.Rows
	if rows, err = d.db.Query(ctx, _sqlQueryRoomByStatus, model.LiveStatusOpen, pageSize, (page-1)*pageSize); err != nil {
		log.Error("RawGetAllLivingRoomsInfo.Query error(%+v)", err)
		return
	}
	defer rows.Close()
	for rows.Next() {
		l := &model.Live{}
		if err = rows.Scan(&l.RoomID, &l.CatalogID, &l.Title, &l.Intro, &l.LiveStartTime, &l.CreateTime, &l.UserID, &l.Score); err != nil {
			log.Error("RawGetAllLivingRoomsInfo.Scan error(%+v)", err)
			return
		}
		res = append(res, l)
	}
	err = rows.Err()

	return
}

type InterventionCard struct {
	CardID          int64
	CardType        int64
	ElementID       int64
	StartTime       int64
	EndTime         int64
	ExposureLevelID int64
	ExposureCurrent int64
	CatalogID       int64
	OperateSource   int64
}

const (
	InterventionCardTypeDrama = 1 // 剧集卡
	InterventionCardTypeLive  = 2 // 直播卡
)

// InterventionCardOperateSource 运营干预卡来源
const (
	InterventionCardOperateSourceTrafficCard           = 0 // 曝光流量卡
	InterventionCardOperateSourceGuildRecommend        = 1 // 公会推荐位直播
	InterventionCardOperateSourceHotFixed              = 2 // 热门列表固定主播
	InterventionCardOperateSourceOperateRecommend      = 3 // 运营推荐主播
	InterventionCardOperateSourceLiveRecommendSchedule = 4 // 直播推荐排期
)

type InterventionCardList struct {
	List       []*InterventionCard
	Pagination *Pagination
}

func (d *dao) queryHomeFeedInterventionCardsCount(ctx context.Context) (int64, error) {
	sqlCountQuery := `
		SELECT COUNT(*) FROM (
			(
				SELECT 1
				FROM m_recommended_elements m
				WHERE m.module_type = ? AND m.archive = 0
					AND m.start_time <= UNIX_TIMESTAMP()
					AND (m.end_time >= UNIX_TIMESTAMP() OR m.end_time = 0)
					AND m.more IS NOT NULL
					AND JSON_EXTRACT(m.more, '$.exposure_level_id') IS NOT NULL
			)
			UNION ALL
			(
				SELECT 1
				FROM live_recommended_elements l
				WHERE l.sort > 0
					AND l.element_type = ?
					AND (l.attr & 8) = 0
					AND l.start_time <= UNIX_TIMESTAMP()
					AND l.expire_time > UNIX_TIMESTAMP()
			)
		) as total_cards`

	var count int64
	err := d.db.QueryRow(
		ctx, sqlCountQuery,
		mrecommendedelements.ModuleTypeHomeFeed,
		liverecommendedelements.ElementTypeLiveRecommend,
	).Scan(&count)
	return count, err
}

func (d *dao) queryHomeFeedInterventionCardsList(ctx context.Context, pageSize int64, offset int64) ([]*InterventionCard, error) {
	sqlUnionQuery := `
		(
			-- m_recommended_elements 查询
			SELECT
				m.id, m.element_id, m.start_time, m.end_time,
				JSON_EXTRACT(m.more, '$.exposure_level_id') as exposure_level_id,
				IFNULL(d1.exposure_count, 0) as exposure_count,
				CASE m.element_type
					WHEN ? THEN ?  -- 剧集卡
					WHEN ? THEN ?  -- 直播卡
				END as card_type,
				? as operate_source  -- 曝光流量卡
			FROM m_recommended_elements m
			LEFT JOIN m_recommended_elements_daily_exposure d1
				ON m.id = d1.element_id AND d1.bizdate = CURDATE() AND d1.scene = ?
			WHERE m.module_type = ? AND m.archive = 0
				AND m.start_time <= UNIX_TIMESTAMP()
				AND (m.end_time >= UNIX_TIMESTAMP() OR m.end_time = 0)
				AND m.more IS NOT NULL
				AND JSON_EXTRACT(m.more, '$.exposure_level_id') IS NOT NULL
		)
		UNION ALL
		(
			-- live_recommended_elements 查询
			SELECT
				l.id, l.element_id, l.start_time, l.expire_time,
				COALESCE(JSON_EXTRACT(l.extended_fields, '$.exposure_level_id'), 0) as exposure_level_id,
				IFNULL(d2.exposure_count, 0) as exposure_count,
				? as card_type,  -- 直播卡
				? as operate_source  -- 直播推荐排期
			FROM live_recommended_elements l
			LEFT JOIN m_recommended_elements_daily_exposure d2
				ON l.id = d2.element_id AND d2.bizdate = CURDATE() AND d2.scene = ?
			WHERE l.sort > 0
				AND l.element_type = ?
				AND (l.attr & 8) = 0
				AND l.start_time <= UNIX_TIMESTAMP()
				AND l.expire_time > UNIX_TIMESTAMP()
		)
		ORDER BY id DESC LIMIT ? OFFSET ?`

	rows, err := d.db.Query(
		ctx, sqlUnionQuery,
		// m_recommended_elements 查询参数
		mrecommendedelements.ElementTypeDrama, InterventionCardTypeDrama,
		mrecommendedelements.ElementTypeLive, InterventionCardTypeLive,
		InterventionCardOperateSourceTrafficCard,
		mrecommendedelementsdailyexposure.SceneHomePage,
		mrecommendedelements.ModuleTypeHomeFeed,
		// live_recommended_elements 查询参数
		InterventionCardTypeLive,
		InterventionCardOperateSourceLiveRecommendSchedule,
		mrecommendedelementsdailyexposure.SceneLivePage,
		liverecommendedelements.ElementTypeLiveRecommend,
		// LIMIT OFFSET 参数
		pageSize, offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	cards := make([]*InterventionCard, pageSize)
	for rows.Next() {
		var id, elementID, startTime, endTime, exposureLevelID, exposureCount, cardType, operateSource int64

		if err = rows.Scan(
			&id, &elementID, &startTime, &endTime, &exposureLevelID, &exposureCount, &cardType, &operateSource,
		); err != nil {
			log.Error("queryHomeFeedInterventionCardsList.Scan error(%+v)", err)
			continue
		}

		card := &InterventionCard{
			CardID:          id,
			ElementID:       elementID,
			StartTime:       startTime,
			EndTime:         endTime,
			ExposureLevelID: exposureLevelID,
			ExposureCurrent: exposureCount,
			CardType:        cardType,
			OperateSource:   operateSource,
		}

		cards = append(cards, card)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return cards, nil
}

func (d *dao) RawGetHomeFeedInterventionCards(ctx context.Context, page int64, pageSize int64) (*InterventionCardList, error) {
	offset := (page - 1) * pageSize

	// 查询总数
	count, err := d.queryHomeFeedInterventionCardsCount(ctx)
	if err != nil {
		return nil, err
	}

	// 查询列表
	cardsList, err := d.queryHomeFeedInterventionCardsList(ctx, pageSize, offset)
	if err != nil {
		return nil, err
	}

	cards := &InterventionCardList{
		List: cardsList,
		Pagination: &Pagination{
			Count:    count,
			P:        page,
			PageSize: pageSize,
		},
	}

	// 计算最大页数
	maxPage := count / pageSize
	if count%pageSize != 0 {
		maxPage += 1
	}
	cards.Pagination.MaxPage = maxPage

	return cards, nil
}

func (d *dao) RawGetLivePageInterventionCards(ctx context.Context, page int64, pageSize int64) (*InterventionCardList, error) {
	sqlLivePageInterventionCardsWhere := `
		WHERE
			sort = 4
			AND ((element_type = ? AND JSON_EXTRACT(extended_fields, '$.from') = ?) OR element_type IN (?, ?))
			AND start_time <= UNIX_TIMESTAMP() AND expire_time > UNIX_TIMESTAMP()
		`
	sqlLivePageInterventionCardsCount := "SELECT COUNT(*) FROM live_recommended_elements " + sqlLivePageInterventionCardsWhere
	sqlLivePageInterventionCardsQuery := `
			SELECT
				l.id, l.sort, l.element_id, l.element_type, l.name, l.cover, l.url, l.attr,
				l.start_time, l.expire_time, l.create_time, l.modified_time,
				l.extended_fields, IFNULL(d.exposure_count, 0) as exposure_count
			FROM live_recommended_elements l
			LEFT JOIN m_recommended_elements_daily_exposure d ON l.id = d.element_id AND d.bizdate = CURDATE() AND d.scene = ?
		` + sqlLivePageInterventionCardsWhere + " ORDER BY l.sort DESC LIMIT ? OFFSET ?"

	offset := (page - 1) * pageSize

	// 查询总数
	var count int64
	err := d.db.
		QueryRow(
			ctx, sqlLivePageInterventionCardsCount,
			liverecommendedelements.ElementTypeSquare, liverecommendedelements.FromGuild,
			liverecommendedelements.ElementAlgorithmExposure,
		).
		Scan(&count)
	if err != nil {
		return nil, err
	}

	// 查询列表
	rows, err := d.db.
		Query(
			ctx, sqlLivePageInterventionCardsQuery,
			mrecommendedelementsdailyexposure.SceneLivePage,
			liverecommendedelements.ElementTypeSquare, liverecommendedelements.FromGuild,
			liverecommendedelements.ElementAlgorithmExposure,
			pageSize, offset,
		)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	cards := &InterventionCardList{
		List: make([]*InterventionCard, 0),
		Pagination: &Pagination{
			Count:    count,
			P:        page,
			PageSize: pageSize,
		},
	}

	// 计算最大页数
	maxPage := count / pageSize
	if count%pageSize != 0 {
		maxPage += 1
	}
	cards.Pagination.MaxPage = maxPage

	// 遍历结果
	for rows.Next() {
		r := &liverecommendedelements.Model{}
		var exposureCount int64

		if err = rows.Scan(
			&r.ID, &r.Sort, &r.ElementID, &r.ElementType, &r.Name, &r.Cover, &r.URL, &r.Attr,
			&r.StartTime, &r.ExpireTime, &r.CreateTime, &r.ModifiedTime,
			&r.ExtendedFields, &exposureCount,
		); err != nil {
			log.Error("RawGetLivePageInterventionCards.Scan error(%+v)", err)
			continue
		}

		// 构建卡片
		card := &InterventionCard{
			CardID:          r.ID,
			CardType:        InterventionCardTypeLive,
			ElementID:       r.ElementID,
			EndTime:         r.ExpireTime,
			ExposureCurrent: exposureCount,
		}
		if r.StartTime != nil {
			card.StartTime = int64(*r.StartTime)
		}

		switch r.ElementType {
		case liverecommendedelements.ElementTypeSquare:
			card.OperateSource = InterventionCardOperateSourceGuildRecommend
			// 公会热 4 推荐位固定 C 级曝光量
			card.ExposureLevelID = mrecommendedexposurelevel.LivePageGuildRecommendExposureLevelID
		case liverecommendedelements.ElementAlgorithmExposure:
			card.OperateSource = InterventionCardOperateSourceHotFixed
			catalogID := r.GetCatalogID()
			if catalogID == nil {
				log.Error("RawGetLivePageInterventionCards.CatalogID is nil, card: %+v", r)
				continue
			}
			card.CatalogID = *catalogID
			exposureLevelID := r.GetExposureLevelID()
			if exposureLevelID == 0 {
				log.Error("RawGetLivePageInterventionCards.ExposureLevelID is 0, card: %+v", r)
				continue
			}
			card.ExposureLevelID = exposureLevelID
		default:
			log.Error("RawGetLivePageInterventionCards.Unknown element type, card: %+v", r)
			continue
		}

		cards.List = append(cards.List, card)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return cards, nil
}

// RawGetRecommendedExposureLevels 从数据库中获取推荐曝光等级
func (d *dao) RawGetRecommendedExposureLevels(ctx context.Context) (levels []*mrecommendedexposurelevel.Model, err error) {
	var modelList []*mrecommendedexposurelevel.Model
	rows, err := d.db.Query(ctx, "SELECT id, create_time, modified_time, exposure, level, status, scene FROM m_recommended_exposure_level WHERE status = ?", mrecommendedexposurelevel.StatusEnabled)
	if err != nil {
		log.Error("RawGetRecommendedExposureLevels error(%v)", err)
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var model = new(mrecommendedexposurelevel.Model)
		if err = rows.Scan(&model.ID, &model.CreateTime, &model.ModifiedTime, &model.Exposure, &model.Level, &model.Status, &model.Scene); err != nil {
			log.Error("RawGetRecommendedExposureLevels scan error(%v)", err)
			return nil, err
		}
		modelList = append(modelList, model)
	}
	if err = rows.Err(); err != nil {
		log.Error("RawGetRecommendedExposureLevels rows error(%v)", err)
		return nil, err
	}

	return modelList, nil
}
